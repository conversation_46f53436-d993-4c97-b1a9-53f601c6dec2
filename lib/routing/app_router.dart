import 'package:cussme/domain/domain.dart';
import 'package:cussme/features/about/about_screen.dart';
import 'package:cussme/features/auth/forgot_password/forget_password_screen.dart';
import 'package:cussme/features/auth/reset_password/new/reset_password_new_screen.dart';
import 'package:cussme/features/auth/reset_password/sent/reset_password_sent_screen.dart';
import 'package:cussme/features/auth/reset_password/success/reset_password_success_screen.dart';
import 'package:cussme/features/bookmarks/bookmarks_screen.dart';
import 'package:cussme/features/edit_suggestion/edit_suggestion_screen.dart';
import 'package:cussme/features/intro/intro_screen.dart';
import 'package:cussme/features/payment_plans/payment_plans_screen.dart';
import 'package:cussme/features/report/report_screen.dart';
import 'package:cussme/features/search/search_screen.dart';
import 'package:cussme/features/setting/account_settings/account_settings_screen.dart';
import 'package:cussme/features/setting/languages/languages_screen.dart';
import 'package:cussme/features/setting/preferences/preferences_screen.dart';
import 'package:cussme/features/setting/spiciness/spiciness_screen.dart';
import 'package:cussme/features/setting/sub_info/sub_info_screen.dart';
import 'package:cussme/features/word_detail/word_detail_screen.dart';
import 'package:cussme/features/word_list/word_list_screen.dart';
import 'package:cussme/routing/navigation_data.dart';
import 'package:cussme/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../features/auth/apple_callback/apple_callback_screen.dart';
import '../features/auth/auth_bottom_sheet_modal.dart';
import '../features/auth/signin/signin_screen.dart';
import '../features/auth/signup/signup_screen.dart';
import '../features/home/<USER>';
import '../features/splash/splash_screen.dart';
import 'bottom_sheet_transitions.dart';
import 'route_constants.dart';
import 'route_location.dart';

part 'app_router.g.dart';

final rootNavigatorKey = GlobalKey<NavigatorState>();
final routeObserver = RouteObserver<ModalRoute<void>>();

@riverpod
GoRouter goRouter(Ref ref) => GoRouter(
      navigatorKey: rootNavigatorKey,
      initialLocation: RouteLocation.splash.path,
      routes: $appRoutes,
      debugLogDiagnostics: true,
      observers: [routeObserver],
    );

@TypedGoRoute<SplashRoute>(
  path: splashPath,
  name: splashName,
)
class SplashRoute extends GoRouteData {
  const SplashRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const SplashScreen();
  }
}

@TypedGoRoute<SignInRoute>(
  path: signInPath,
  name: signInName,
)
class SignInRoute extends GoRouteData {
  const SignInRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    final navData = state.extra as NavigationData?;
    return SignInScreen(navigationData: navData);
  }
}

@TypedGoRoute<AppleCallbackRoute>(
  path: appleCallbackPath,
  name: appleCallbackName,
)
class AppleCallbackRoute extends GoRouteData {
  const AppleCallbackRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const AppleCallbackScreen();
  }
}

@TypedGoRoute<SignUpRoute>(
  path: signUpPath,
  name: signUpName,
)
class SignUpRoute extends GoRouteData {
  const SignUpRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    final navData = state.extra as NavigationData?;
    return SignUpScreen(navigationData: navData);
  }
}

@TypedGoRoute<ForgotPasswordRoute>(
  path: forgotPasswordPath,
  name: forgotPasswordName,
)
class ForgotPasswordRoute extends GoRouteData {
  const ForgotPasswordRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const ForgotPasswordScreen();
  }
}

@TypedGoRoute<ResetPasswordNewRoute>(
  path: resetPasswordNewPath,
  name: resetPasswordNewName,
)
class ResetPasswordNewRoute extends GoRouteData {
  const ResetPasswordNewRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    final uri = state.uri;

    if (uri.queryParameters.isEmpty) {
      return const ResetPasswordNewScreen();
    }

    final String? errorDescription = uri.getParameter(errorDescriptionParam);
    final String? token = uri.getParameter(tokenParam);

    final params = ResetPasswordParams(
      errorDescription: errorDescription,
      token: token,
    );

    return ResetPasswordNewScreen(params: params);
  }
}

@TypedGoRoute<ResetPasswordSentRoute>(
  path: resetPasswordSentPath,
  name: resetPasswordSentName,
)
class ResetPasswordSentRoute extends GoRouteData {
  const ResetPasswordSentRoute({this.email});

  final String? email;

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return ResetPasswordSentScreen(email: email);
  }
}

@TypedGoRoute<ResetPasswordSuccessRoute>(
  path: resetPasswordSuccessPath,
  name: resetPasswordSuccessName,
)
class ResetPasswordSuccessRoute extends GoRouteData {
  const ResetPasswordSuccessRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const ResetPasswordSuccessScreen();
  }
}

@TypedGoRoute<HomeRoute>(
  path: homePath,
  name: homeName,
)
class HomeRoute extends GoRouteData {
  final String key;

  const HomeRoute({required this.key});

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return HomeScreen(key: Key(key));
  }
}

@TypedGoRoute<GuestHomeRoute>(
  path: guestHomePath,
  name: guestHomeName,
)
class GuestHomeRoute extends GoRouteData {
  final String key;

  const GuestHomeRoute({required this.key});

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return HomeScreen(key: Key(key));
  }
}

@TypedGoRoute<PreferencesRoute>(
  path: preferencesPath,
  name: preferencesName,
)
class PreferencesRoute extends GoRouteData {
  const PreferencesRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const PreferencesScreen();
  }
}

@TypedGoRoute<AccountSettingsRoute>(
  path: accountSettingsPath,
  name: accountSettingsName,
)
class AccountSettingsRoute extends GoRouteData {
  const AccountSettingsRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const AccountSettingsScreen();
  }
}

@TypedGoRoute<SubInfoRoute>(
  path: subInfoPath,
  name: subInfoName,
)
class SubInfoRoute extends GoRouteData {
  const SubInfoRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const SubInfoScreen();
  }
}

@TypedGoRoute<SpicinessRoute>(
  path: spicinessPath,
  name: spicinessName,
)
class SpicinessRoute extends GoRouteData {
  const SpicinessRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return const SpicinessScreen();
  }
}

@TypedGoRoute<LanguagesRoute>(
  path: languagesPath,
  name: languagesName,
)
class LanguagesRoute extends GoRouteData {
  final String source;

  const LanguagesRoute({required this.source});

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return LanguagesScreen(source: source);
  }
}

@TypedGoRoute<IntroRoute>(
  path: introPath,
  name: introName,
)
class IntroRoute extends GoRouteData {
  const IntroRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    final navData = state.extra as NavigationData?;
    return IntroScreen(navigationData: navData);
  }
}

@TypedGoRoute<WordDetailRoute>(
  path: wordDetailPath,
  name: wordDetailName,
)
class WordDetailRoute extends GoRouteData {
  final String wordId;
  final String? key;

  const WordDetailRoute({required this.wordId, this.key});

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return WordDetailScreen(
      key: Key(key ?? getUniqueTimeBasedKey()),
      wordId: wordId,
    );
  }
}

@TypedGoRoute<EditSuggestionRoute>(
  path: editSuggestionPath,
  name: editSuggestionName,
)
class EditSuggestionRoute extends GoRouteData {
  const EditSuggestionRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    final word = state.extra as WordEntity;
    return EditSuggestionScreen(word: word);
  }
}

@TypedGoRoute<ReportRoute>(
  path: reportPath,
  name: reportName,
)
class ReportRoute extends GoRouteData {
  const ReportRoute();

  @override
  Widget build(BuildContext context, GoRouterState state) {
    final word = state.extra as WordEntity;
    return ReportScreen(word: word);
  }
}

@TypedGoRoute<BookmarksRoute>(
  path: bookmarksPath,
  name: bookmarksName,
)
class BookmarksRoute extends GoRouteData {
  final String key;

  const BookmarksRoute({required this.key});

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return BookmarksScreen(key: Key(key));
  }
}

@TypedGoRoute<WordListRoute>(
  path: wordListPath,
  name: wordListName,
)
class WordListRoute extends GoRouteData {
  final String key;
  final String source;

  const WordListRoute({required this.key, required this.source});

  @override
  Widget build(BuildContext context, GoRouterState state) {
    final navData = state.extra as NavigationData;
    return WordListScreen(
      key: Key(navData.key ?? key),
      language: navData.language!,
      source: source,
    );
  }
}

@TypedGoRoute<SearchRoute>(
  path: searchPath,
  name: searchName,
)
class SearchRoute extends GoRouteData {
  final String key;

  const SearchRoute({required this.key});

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return SearchScreen(key: Key(key));
  }
}

@TypedGoRoute<AboutRoute>(
  path: aboutPath,
  name: aboutName,
)
class AboutRoute extends GoRouteData {
  final bool isPremium;

  const AboutRoute(this.isPremium);

  @override
  Widget build(BuildContext context, GoRouterState state) {
    return AboutScreen(isPremium: isPremium);
  }
}

@TypedGoRoute<PaymentPlansRoute>(
  path: paymentPlansPath,
  name: paymentPlansName,
)
class PaymentPlansRoute extends GoRouteData {
  const PaymentPlansRoute();

  @override
  Page<void> buildPage(BuildContext context, GoRouterState state) {
    return const PaymentPlansScreen().toBottomSheetPage(
      name: paymentPlansName,
      key: state.pageKey,
    );
  }
}

// Extension methods for easier navigation
extension GoRouterExtensions on GoRouter {
  void goToHome() =>
      go('${RouteLocation.home.path}?$keyParam=${getUniqueTimeBasedKey()}');
  void goToGuestHome() => go(
      '${RouteLocation.guestHome.path}?$keyParam=${getUniqueTimeBasedKey()}');
  void goToSignIn(
      {bool registerAsDestination = false, NavigationData? navigationData}) {
    if (registerAsDestination) {
      go(RouteLocation.signIn.path,
          extra: NavigationData(destination: lastUri));
    } else if (navigationData != null) {
      go(RouteLocation.signIn.path, extra: navigationData);
    } else {
      go(RouteLocation.signIn.path);
    }
  }

  void goToSignUp(
      {bool registerAsDestination = false, NavigationData? navigationData}) {
    if (registerAsDestination) {
      go(RouteLocation.signUp.path,
          extra: NavigationData(destination: lastUri));
    } else if (navigationData != null) {
      go(RouteLocation.signUp.path, extra: navigationData);
    } else {
      go(RouteLocation.signUp.path);
    }
  }

  void goToForgotPassword() => go(RouteLocation.forgotPassword.path);
  void goToPreferences() => go(RouteLocation.preferences.path);
  void goToAccountSettings() => go(RouteLocation.accountSettings.path);
  void goToSpiciness() => go(RouteLocation.spiciness.path);
  void goToLanguages({String? source}) =>
      go('${RouteLocation.languages.path}?$sourceParam=$source');

  void goToIntro({NavigationData? navigationData}) => navigationData != null
      ? go(RouteLocation.intro.path, extra: navigationData)
      : go(RouteLocation.intro.path);

  void goToBookmarks() => go(
      '${RouteLocation.bookmarks.path}?$keyParam=${getUniqueTimeBasedKey()}');
  void goToSearch() =>
      go('${RouteLocation.search.path}?$keyParam=${getUniqueTimeBasedKey()}');
  void goToAbout(bool isPremium) =>
      go('${RouteLocation.about.path}?$isPremiumParam=$isPremium');
  void goToPaymentPlans() => go(RouteLocation.paymentPlans.path);

  void goToResetPasswordSent({String? email}) {
    go('${RouteLocation.resetPasswordSent.path}?$emailParam=$email');
  }

  void goToResetPasswordSuccess() =>
      go(RouteLocation.resetPasswordSuccess.path);
  void goToWordDetail({required String wordId}) => go(
      '${RouteLocation.wordDetail.path}?$wordIdParam=$wordId&$keyParam=${getUniqueTimeBasedKey()}');

  // Push methods that maintain navigation stack
  void pushToHome() =>
      push('${RouteLocation.home.path}?$keyParam=${getUniqueTimeBasedKey()}');
  void pushToSignIn({
    bool hasCustomDest = false,
    bool autoDestination = true,
    NavigationData? navigationData,
  }) {
    Object? extra;
    if (hasCustomDest) {
      final destination =
          autoDestination ? lastUri : navigationData?.destination;
      extra = navigationData?.copyWith(
              destination: destination, key: getUniqueTimeBasedKey()) ??
          NavigationData(
              destination: destination, key: getUniqueTimeBasedKey());
    }

    push(RouteLocation.signIn.path, extra: extra);
  }

  void pushToSignUp({NavigationData? navigationData}) => navigationData != null
      ? push(RouteLocation.signUp.path, extra: navigationData)
      : push(RouteLocation.signUp.path);

  void pushToForgotPassword() => push(RouteLocation.forgotPassword.path);
  void pushToResetPasswordNew() => push(RouteLocation.resetPasswordNew.path);
  void pushToPreferences() => push(RouteLocation.preferences.path);
  void pushToAccountSettings() => push(RouteLocation.accountSettings.path);
  void pushToSubInfo() => push(RouteLocation.subInfo.path);
  void pushToSpiciness() => push(RouteLocation.spiciness.path);
  void pushToBookmarks() => push(
      '${RouteLocation.bookmarks.path}?$keyParam=${getUniqueTimeBasedKey()}');
  void pushToSearch() =>
      push('${RouteLocation.search.path}?$keyParam=${getUniqueTimeBasedKey()}');
  void pushToAbout(bool isPremium) =>
      push('${RouteLocation.about.path}?$isPremiumParam=$isPremium');
  void pushToPaymentPlans() => push(RouteLocation.paymentPlans.path);
  void pushToLanguages({required String source}) =>
      push('${RouteLocation.languages.path}?$sourceParam=$source');
  void pushToIntro({NavigationData? navigationData}) => navigationData != null
      ? push(RouteLocation.intro.path, extra: navigationData)
      : push(RouteLocation.intro.path);

  void pushToResetPasswordSent({String? email}) =>
      push('${RouteLocation.resetPasswordSent.path}?$emailParam=$email');
  void pushToResetPasswordSuccess() =>
      push(RouteLocation.resetPasswordSuccess.path);
  void pushToWordDetail({required String wordId}) => push(
      '${RouteLocation.wordDetail.path}?$wordIdParam=$wordId&$keyParam=${getUniqueTimeBasedKey()}');

  void pushToEditSuggestion({required WordEntity word}) =>
      push(RouteLocation.editSuggestion.path, extra: word);

  void pushToReport({required WordEntity word}) =>
      push(RouteLocation.report.path, extra: word);

  void pushToWordList({
    required LanguageEntity language,
    required String source,
  }) =>
      push(
        "${RouteLocation.wordList.path}?$keyParam=${getUniqueTimeBasedKey()}&$sourceParam=$source",
        extra: NavigationData(language: language),
      );

  // PushReplace methods that maintain navigation stack
  void pushReplaceToResetPasswordSent({String? email}) => pushReplacement(
      '${RouteLocation.resetPasswordSent.path}?$emailParam=$email');
  void pushReplaceToResetPasswordSuccess() =>
      pushReplacement(RouteLocation.resetPasswordSuccess.path);

  void pushToPremium(bool isGuest) =>
      isGuest ? _showAuthBottomSheet() : pushToPaymentPlans();

  void _showAuthBottomSheet() {
    final context = rootNavigatorKey.currentContext;
    if (context != null) {
      showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        backgroundColor: Colors.transparent,
        builder: (context) => const AuthBottomSheetModal(),
      );
    }
  }

  void goToDestinationWithHomeInStack(NavigationData navigationData) {
    try {
      goToHome();
      if (navigationData.shouldSkipDestination) return;
      push(navigationData.destination!, extra: navigationData);
    } catch (e) {
      goToHome();
    }
  }

  // Helper methods
  String get lastPath {
    final RouteMatch lastMatch = routerDelegate.currentConfiguration.last;
    final RouteMatchList matchList = lastMatch is ImperativeRouteMatch
        ? lastMatch.matches
        : routerDelegate.currentConfiguration;
    return matchList.fullPath;
  }

  String get lastUri {
    final RouteMatch lastMatch = routerDelegate.currentConfiguration.last;
    final RouteMatchList matchList = lastMatch is ImperativeRouteMatch
        ? lastMatch.matches
        : routerDelegate.currentConfiguration;
    return matchList.uri.toString();
  }

  Future<void> _popUntilRoute(String route) async {
    while (canPop() && lastPath != route) {
      pop();
      await Future.delayed(const Duration(milliseconds: 10));
    }
  }

  Future<void> popUntilSignIn() async {
    await _popUntilRoute(RouteLocation.signIn.path);
  }
}
