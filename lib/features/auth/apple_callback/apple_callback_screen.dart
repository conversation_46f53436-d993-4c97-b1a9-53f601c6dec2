import 'package:cussme/ui/ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

class AppleCallbackScreen extends ConsumerStatefulWidget {
  const AppleCallbackScreen({super.key});

  @override
  ConsumerState<AppleCallbackScreen> createState() =>
      _AppleCallbackScreenState();
}

class _AppleCallbackScreenState extends ConsumerState<AppleCallbackScreen> {
  @override
  void initState() {
    super.initState();
    // Handle the Apple callback and redirect to appropriate screen
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _handleAppleCallback();
    });
  }

  void _handleAppleCallback() {
    // Get the current URI to check for any parameters
    final uri = GoRouterState.of(context).uri;

    // Check if there are any error parameters
    if (uri.queryParameters.containsKey('error')) {
      // Handle error case - redirect to sign in with error
      context.go('/sign-in');
      return;
    }

    // For successful Apple authentication, the sign_in_with_apple package
    // handles the token exchange automatically in the background.
    // The callback URL is just to satisfy Apple's requirements.
    // We should redirect to home or the appropriate screen.

    // Check if user is already authenticated and redirect accordingly
    // For now, redirect to home as the authentication should be complete
    context.go('/home');
  }

  @override
  Widget build(BuildContext context) {
    return const AdMobScaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Processing Apple Sign In...'),
          ],
        ),
      ),
    );
  }
}
